// Configuration constants
export const SUPABASE_CONFIG = {
  url: process.env.EXPO_PUBLIC_SUPABASE_URL || 'your-supabase-url',
  anonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'your-supabase-anon-key',
};

export const PAYSTACK_CONFIG = {
  publicKey: process.env.EXPO_PUBLIC_PAYSTACK_PUBLIC_KEY || 'your-paystack-public-key',
  currency: 'KES',
};

// App constants
export const APP_CONSTANTS = {
  APP_NAME: 'Sunset View Hotel',
  SUPPORT_EMAIL: '<EMAIL>',
  SUPPORT_PHONE: '+254-700-123-456',
  WEBSITE: 'www.sunsetviewhotel.com',
  ADDRESS: 'Diani Beach Road, Mombasa, Kenya',
  TAGLINE: 'Luxury • Comfort • Excellence',
};

// Room types
export const ROOM_TYPES = {
  STANDARD: 'standard',
  DELUXE: 'deluxe',
  SUITE: 'suite',
  PRESIDENTIAL: 'presidential',
} as const;

// Room statuses
export const ROOM_STATUS = {
  AVAILABLE: 'available',
  BOOKED: 'booked',
  MAINTENANCE: 'maintenance',
  CLEANING: 'cleaning',
} as const;

// Reservation statuses
export const RESERVATION_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  CHECKED_IN: 'checked_in',
  CHECKED_OUT: 'checked_out',
  CANCELLED: 'cancelled',
} as const;

// Payment statuses
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PAID: 'paid',
  FAILED: 'failed',
  REFUNDED: 'refunded',
} as const;

// User roles
export const USER_ROLES = {
  GUEST: 'guest',
  RECEPTIONIST: 'receptionist',
  ADMIN: 'admin',
} as const;

// Common amenities
export const COMMON_AMENITIES = [
  'WiFi',
  'Air Conditioning',
  'TV',
  'Mini Bar',
  'Room Service',
  'Balcony',
  'Ocean View',
  'Jacuzzi',
  'Kitchenette',
  'Work Desk',
  'Safe',
  'Telephone',
];

// Modern Color System with Light and Dark Themes
export const lightColors = {
  // Primary colors
  primary: '#2E8B57', // Sea Green
  primaryLight: '#90EE90',
  primaryDark: '#228B22',
  accent: '#FFD700', // Gold
  accentLight: '#FFFFE0',
  accentDark: '#DAA520',

  // Semantic colors
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',

  // Surface colors
  background: '#FFFFFF',
  surface: '#FFFFFF',
  surfaceVariant: '#F8F9FA',
  card: '#FFFFFF',

  // Text colors
  text: '#1A1A1A',
  textPrimary: '#1A1A1A',
  textSecondary: '#6B7280',
  textTertiary: '#9CA3AF',
  textLight: '#9CA3AF',

  // Other colors
  border: '#E5E7EB',
  divider: '#F3F4F6',
  white: '#FFFFFF',
  onSurfaceVariant: '#6B7280',
  onSurface: '#1A1A1A',
  onPrimary: '#FFFFFF',
  outline: '#D1D5DB',
  disabled: '#9CA3AF',

  // RGB values for charts (needed for react-native-chart-kit)
  primaryRGB: '46, 139, 87',
  onSurfaceRGB: '26, 26, 26',
  successRGB: '16, 185, 129',
  warningRGB: '245, 158, 11',
  errorRGB: '239, 68, 68',
  infoRGB: '59, 130, 246',
};

export const darkColors = {
  // Primary colors
  primary: '#4ADE80', // Lighter green for dark mode
  primaryLight: '#86EFAC',
  primaryDark: '#16A34A',
  accent: '#FCD34D', // Softer gold for dark mode
  accentLight: '#FEF3C7',
  accentDark: '#D97706',

  // Semantic colors
  success: '#22C55E',
  warning: '#F59E0B',
  error: '#F87171',
  info: '#60A5FA',

  // Surface colors
  background: '#000000',
  surface: '#0A0A0A',
  surfaceVariant: '#171717',
  card: '#0A0A0A',

  // Text colors
  text: '#FFFFFF',
  textPrimary: '#FFFFFF',
  textSecondary: '#A3A3A3',
  textTertiary: '#737373',
  textLight: '#737373',

  // Other colors
  border: '#262626',
  divider: '#171717',
  white: '#FFFFFF',
  onSurfaceVariant: '#A3A3A3',
  onSurface: '#FFFFFF',
  onPrimary: '#000000',
  outline: '#404040',
  disabled: '#525252',

  // RGB values for charts (needed for react-native-chart-kit)
  primaryRGB: '74, 222, 128',
  onSurfaceRGB: '255, 255, 255',
  successRGB: '34, 197, 94',
  warningRGB: '245, 158, 11',
  errorRGB: '248, 113, 113',
  infoRGB: '96, 165, 250',
};

// Current colors (will be replaced by theme context)
export const colors = lightColors;

// Extended color system for charts and specific use cases
export const extendedColors = {
  // Chart colors
  chart: {
    primary: '#2E8B57',
    secondary: '#FFD700',
    tertiary: '#3B82F6',
    quaternary: '#10B981',
    error: '#EF4444',
    warning: '#F59E0B',
  },

  // RGB values for charts
  rgb: {
    primary: 'rgb(46, 139, 87)',
    secondary: 'rgb(255, 215, 0)',
    tertiary: 'rgb(59, 130, 246)',
    quaternary: 'rgb(16, 185, 129)',
    error: 'rgb(239, 68, 68)',
    warning: 'rgb(245, 158, 11)',
    success: 'rgb(16, 185, 129)',
    info: 'rgb(59, 130, 246)',
    text: 'rgb(26, 26, 26)',
    textSecondary: 'rgb(107, 114, 128)',
    background: 'rgb(248, 249, 250)',
    surface: 'rgb(255, 255, 255)',
  },

  // Room type colors
  roomTypes: {
    standard: '#6B7280',
    deluxe: '#3B82F6',
    suite: '#8B5CF6',
    presidential: '#F59E0B',
  },

  // Status colors
  status: {
    available: '#10B981',
    booked: '#F59E0B',
    maintenance: '#EF4444',
    cleaning: '#6B7280',
    pending: '#F59E0B',
    confirmed: '#10B981',
    checked_in: '#3B82F6',
    checked_out: '#6B7280',
    cancelled: '#EF4444',
    paid: '#10B981',
    failed: '#EF4444',
    refunded: '#8B5CF6',
  },
};

// Legacy COLORS constant for backward compatibility
export const COLORS = {
  primary: '#FF6B35',
  secondary: '#2C3E50',
  accent: '#F39C12',
  background: '#FFFFFF',
  surface: '#F8F9FA',
  text: '#2C3E50',
  textSecondary: '#7F8C8D',
  border: '#E9ECEF',
  success: '#27AE60',
  warning: '#F39C12',
  error: '#E74C3C',
  info: '#3498DB',
};

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  // Legacy aliases
  small: 8,
  medium: 16,
  large: 24,
  extraLarge: 48,
};

// Typography
export const TYPOGRAPHY = {
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
  },
  weights: {
    light: '300' as const,
    normal: 'normal' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: 'bold' as const,
  },
  // Pre-defined styles for convenience
  h1: { fontSize: 32, fontWeight: 'bold' as const },
  h2: { fontSize: 28, fontWeight: 'bold' as const },
  h3: { fontSize: 24, fontWeight: '600' as const },
  h4: { fontSize: 20, fontWeight: '600' as const },
  body1: { fontSize: 16, fontWeight: 'normal' as const },
  body2: { fontSize: 14, fontWeight: 'normal' as const },
  caption: { fontSize: 12, fontWeight: 'normal' as const },
  body: { fontSize: 16, fontWeight: 'normal' as const },
  subtitle: { fontSize: 18, fontWeight: '500' as const },
};

// Export aliases for consistency
export const Colors = lightColors;  // Fixed: Changed from undefined 'colors' to 'lightColors'
export const colors = lightColors;  // Added for lowercase compatibility
export const Spacing = SPACING;
export const Typography = TYPOGRAPHY;
export const spacing = SPACING;
export const typography = TYPOGRAPHY;
