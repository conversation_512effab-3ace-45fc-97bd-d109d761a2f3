import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Switch,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Avatar,
  Surface,
  Divider,
  List,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';

import { useAuthStore } from '../../store/authStore';
import { Spacing, Typography, Colors } from '../../constants';
import { getInitials } from '../../utils/stringUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { CustomTextInput } from '../../components/ui/CustomTextInput';
import { CustomButton } from '../../components/ui/CustomButton';
import { ThemeSelector } from '../../components/ui/ThemeSelector';
import { ThemeTest } from '../../components/ui/ThemeTest';

export const ProfileScreen = () => {
  const { user, signOut, updateProfile } = useAuthStore();
  const { colors } = useTheme();
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState({
    fullName: user?.full_name || '',
    phone: user?.phone || '',
  });
  const [notifications, setNotifications] = useState({
    bookingUpdates: true,
    promotions: false,
    newsletter: false,
  });

  const handleSaveProfile = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const result = await updateProfile({
        full_name: form.fullName,
        phone: form.phone,
      });

      if (result.success) {
        setEditing(false);
        Alert.alert('Success', 'Profile updated successfully');
      } else {
        Alert.alert('Error', result.error || 'Failed to update profile');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => signOut()
        }
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // Implement account deletion
            Alert.alert('Feature Coming Soon', 'Account deletion will be available in a future update');
          }
        }
      ]
    );
  };

  if (!user) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: colors.background }]}>
        <MaterialIcons name="error-outline" size={64} color={colors.error} />
        <Text style={[styles.errorText, { color: colors.error }]}>Unable to load profile</Text>
      </View>
    );
  }

  const styles = createStyles(colors);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Profile Header */}
      <Surface style={styles.headerCard}>
        <View style={styles.profileHeader}>
          <Avatar.Text
            size={80}
            label={getInitials(user.full_name)}
            style={styles.avatar}
          />
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{user.full_name}</Text>
            <Text style={styles.userEmail}>{user.email}</Text>
            <Text style={styles.userRole}>{user.role}</Text>
          </View>
        </View>
      </Surface>

      {/* Profile Information */}
      <Card style={styles.infoCard}>
        <Card.Content>
          <View style={styles.cardHeader}>
            <Text style={styles.sectionTitle}>Profile Information</Text>
            <Button
              mode="text"
              onPress={() => {
                if (editing) {
                  setForm({
                    fullName: user.full_name,
                    phone: user.phone || '',
                  });
                }
                setEditing(!editing);
              }}
              disabled={loading}
            >
              {editing ? 'Cancel' : 'Edit'}
            </Button>
          </View>

          {editing ? (
            <View style={styles.editForm}>
              <CustomTextInput
                label="Full Name"
                value={form.fullName}
                onChangeText={(text) => setForm(prev => ({ ...prev, fullName: text }))}
                style={styles.input}
              />
              <CustomTextInput
                label="Phone Number"
                value={form.phone}
                onChangeText={(text) => setForm(prev => ({ ...prev, phone: text }))}
                keyboardType="phone-pad"
                style={styles.input}
              />
              <CustomButton
                title="Save Changes"
                onPress={handleSaveProfile}
                loading={loading}
                disabled={loading}
                style={styles.saveButton}
              />
            </View>
          ) : (
            <View style={styles.infoList}>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Full Name</Text>
                <Text style={styles.infoValue}>{user.full_name}</Text>
              </View>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Email</Text>
                <Text style={styles.infoValue}>{user.email}</Text>
              </View>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Phone</Text>
                <Text style={styles.infoValue}>{user.phone || 'Not provided'}</Text>
              </View>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Member Since</Text>
                <Text style={styles.infoValue}>
                  {new Date(user.created_at).toLocaleDateString()}
                </Text>
              </View>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Notification Preferences */}
      <Card style={styles.notificationsCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Notification Preferences</Text>
          
          <View style={styles.notificationItem}>
            <View style={styles.notificationInfo}>
              <Text style={styles.notificationTitle}>Booking Updates</Text>
              <Text style={styles.notificationDesc}>
                Get notified about your reservation status
              </Text>
            </View>
            <Switch
              value={notifications.bookingUpdates}
              onValueChange={(value) => 
                setNotifications(prev => ({ ...prev, bookingUpdates: value }))
              }
            />
          </View>

          <View style={styles.notificationItem}>
            <View style={styles.notificationInfo}>
              <Text style={styles.notificationTitle}>Promotions</Text>
              <Text style={styles.notificationDesc}>
                Receive special offers and discounts
              </Text>
            </View>
            <Switch
              value={notifications.promotions}
              onValueChange={(value) => 
                setNotifications(prev => ({ ...prev, promotions: value }))
              }
            />
          </View>

          <View style={styles.notificationItem}>
            <View style={styles.notificationInfo}>
              <Text style={styles.notificationTitle}>Newsletter</Text>
              <Text style={styles.notificationDesc}>
                Stay updated with hotel news and events
              </Text>
            </View>
            <Switch
              value={notifications.newsletter}
              onValueChange={(value) => 
                setNotifications(prev => ({ ...prev, newsletter: value }))
              }
            />
          </View>
        </Card.Content>
      </Card>

      {/* Theme Settings */}
      <ThemeSelector />

      {/* Theme Test Component */}
      <ThemeTest />

      {/* App Settings */}
      <Card style={styles.settingsCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>App Settings</Text>
          
          <List.Item
            title="Privacy Policy"
            description="Read our privacy policy"
            left={(props) => <List.Icon {...props} icon="shield-account" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('Coming Soon', 'Privacy policy will be available soon')}
          />
          
          <List.Item
            title="Terms of Service"
            description="Read our terms and conditions"
            left={(props) => <List.Icon {...props} icon="file-document" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('Coming Soon', 'Terms of service will be available soon')}
          />
          
          <List.Item
            title="Help & Support"
            description="Get help and contact support"
            left={(props) => <List.Icon {...props} icon="help-circle" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('Support', 'Contact <NAME_EMAIL>')}
          />
          
          <List.Item
            title="About"
            description="App version and information"
            left={(props) => <List.Icon {...props} icon="information" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => Alert.alert('About', 'Sunset View Hotel App v1.0.0')}
          />
        </Card.Content>
      </Card>

      {/* Actions */}
      <View style={styles.actionsContainer}>
        <CustomButton
          title="Sign Out"
          variant="outline"
          onPress={handleSignOut}
          style={styles.actionButton}
        />

        <CustomButton
          title="Delete Account"
          variant="danger"
          onPress={handleDeleteAccount}
          style={[styles.actionButton, styles.deleteButton]}
        />
      </View>
    </ScrollView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  errorText: {
    ...Typography.h4,
    color: colors.error,
    marginTop: Spacing.medium,
  },
  headerCard: {
    margin: Spacing.medium,
    padding: Spacing.large,
    borderRadius: 12,
    elevation: 4,
    backgroundColor: colors.surface,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: colors.primary,
    marginRight: Spacing.large,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    ...Typography.h3,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 2,
  },
  userEmail: {
    ...Typography.body,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  userRole: {
    ...Typography.caption,
    color: colors.primary,
    textTransform: 'capitalize',
  },
  infoCard: {
    margin: Spacing.medium,
    marginVertical: Spacing.small,
    backgroundColor: colors.surface,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.medium,
  },
  sectionTitle: {
    ...Typography.h4,
    fontWeight: 'bold',
    color: colors.text,
  },
  editForm: {
    gap: Spacing.medium,
  },
  input: {
    marginBottom: Spacing.small,
  },
  saveButton: {
    marginTop: Spacing.small,
  },
  infoList: {
    gap: Spacing.medium,
  },
  infoItem: {
    gap: 4,
  },
  infoLabel: {
    ...Typography.caption,
    color: colors.textSecondary,
  },
  infoValue: {
    ...Typography.body,
    color: colors.text,
    fontWeight: '500',
  },
  notificationsCard: {
    margin: Spacing.medium,
    marginVertical: Spacing.small,
    backgroundColor: colors.surface,
  },
  notificationItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: Spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  notificationInfo: {
    flex: 1,
    marginRight: Spacing.medium,
  },
  notificationTitle: {
    ...Typography.subtitle,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 2,
  },
  notificationDesc: {
    ...Typography.caption,
    color: colors.textSecondary,
  },
  settingsCard: {
    margin: Spacing.medium,
    marginVertical: Spacing.small,
    backgroundColor: colors.surface,
  },
  actionsContainer: {
    padding: Spacing.medium,
    paddingBottom: Spacing.xxl,
    gap: Spacing.medium,
  },
  actionButton: {
    paddingVertical: Spacing.small,
  },
  deleteButton: {
    borderColor: colors.error,
  },
});
