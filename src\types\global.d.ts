// Global type declarations

// Node.js global variables
declare var process: {
  env: {
    [key: string]: string | undefined;
    EXPO_PUBLIC_SUPABASE_URL?: string;
    EXPO_PUBLIC_SUPABASE_ANON_KEY?: string;
    EXPO_PUBLIC_PAYSTACK_PUBLIC_KEY?: string;
  };
};

// Global type augmentations

// Declare UI and navigation libraries
declare module 'react-native' {
  export const View: any;
  export const Text: any;
  export const StyleSheet: any;
  export const ScrollView: any;
  export const Alert: any;
  export const Image: any;
  export const TouchableOpacity: any;
  export const TextInput: any;
  export const Switch: any;
  export * from 'react-native';
}
declare module 'react-native-paper' {
  export const Text: any;
  export const Card: any;
  export const Button: any;
  export const Avatar: any;
  export const Chip: any;
  export const Divider: any;
  export const IconButton: any;
  export const PaperProvider: any;
  export const Surface: any;
  export * from 'react-native-paper';
}
declare module '@react-navigation/native' {
  export const useNavigation: any;
  export const useRoute: any;
  export * from '@react-navigation/native';
}
declare module '@react-navigation/native-stack';
declare module '@react-navigation/bottom-tabs';
declare module '@react-navigation/stack';

// Declare Expo modules
declare module '@expo/vector-icons' {
  export const MaterialIcons: any;
  export const Ionicons: any;
  export * from '@expo/vector-icons';
}
declare module 'expo-image-picker';
declare module 'expo-image-manipulator';
declare module 'expo-device';
declare module 'expo-notifications';
declare module 'expo-file-system';
declare module 'expo-sharing';
declare module 'react-native-html-to-pdf';

// Declare other modules
declare module 'react-native-super-grid';
declare module 'react-native-modal-datetime-picker';
declare module '@react-native-community/datetimepicker';
declare module 'react-native-toast-message';
declare module 'react-native-chart-kit';
declare module 'react-native-paystack-webview' {
  export interface PaystackProps {
    paystackKey: string;
    amount: number;
    billingEmail: string;
    billingName?: string;
    billingMobile?: string;
    currency?: string;
    reference?: string;
    channels?: string[];
    metadata?: any;
    onCancel: () => void;
    onSuccess: (response: any) => void;
    autoStart?: boolean;
  }

  export const Paystack: React.ComponentType<PaystackProps>;
}
declare module 'expo-status-bar' {
  export const StatusBar: any;
  export * from 'expo-status-bar';
}
declare module 'react-native-safe-area-context' {
  export const SafeAreaProvider: any;
  export const SafeAreaView: any;
  export const useSafeAreaInsets: any;
  export * from 'react-native-safe-area-context';
}
declare module '@supabase/supabase-js' {
  export const createClient: any;
  export * from '@supabase/supabase-js';
}
declare module 'zustand' {
  export const create: any;
  export * from 'zustand';
}

// For image imports with require()
declare module '*.png' {
  const content: any;
  export default content;
}
declare module '*.jpg' {
  const content: any;
  export default content;
}
declare module '*.jpeg' {
  const content: any;
  export default content;
}
declare module '*.gif' {
  const content: any;
  export default content;
}
declare module '*.svg' {
  const content: any;
  export default content;
}

// Add any other modules that need declarations
