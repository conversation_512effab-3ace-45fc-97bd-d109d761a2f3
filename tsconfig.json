{"compilerOptions": {"target": "esnext", "lib": ["dom", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-native", "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "strictNullChecks": false, "strictPropertyInitialization": false, "typeRoots": ["node_modules/@types", "./src/types"], "types": ["node"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "src/types/global.d.ts", "src/types/react-fixes.d.ts", "src/types/react-native-paper-fixes.d.ts", "types.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"], "extends": "expo/tsconfig.base"}