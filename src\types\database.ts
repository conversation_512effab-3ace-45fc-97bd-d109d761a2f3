// Database type definitions for Supabase

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string;
          phone: string | null;
          role: 'guest' | 'receptionist' | 'admin';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          full_name: string;
          phone?: string | null;
          role?: 'guest' | 'receptionist' | 'admin';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string;
          phone?: string | null;
          role?: 'guest' | 'receptionist' | 'admin';
          created_at?: string;
          updated_at?: string;
        };
      };
      rooms: {
        Row: {
          id: string;
          room_number: string;
          room_type: 'standard' | 'deluxe' | 'suite' | 'presidential';
          price_per_night: number;
          description: string;
          amenities: string[];
          max_occupancy: number;
          images: Array<{
            id: string;
            url: string;
            alt_text?: string;
            upload_date?: string;
            file_name?: string;
            file_size?: number;
          }>;
          status: 'available' | 'booked' | 'maintenance' | 'cleaning';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          room_number: string;
          room_type: 'standard' | 'deluxe' | 'suite' | 'presidential';
          price_per_night: number;
          description: string;
          amenities?: string[];
          max_occupancy: number;
          images?: Array<{
            id: string;
            url: string;
            alt_text?: string;
            upload_date?: string;
            file_name?: string;
            file_size?: number;
          }>;
          status?: 'available' | 'booked' | 'maintenance' | 'cleaning';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          room_number?: string;
          room_type?: 'standard' | 'deluxe' | 'suite' | 'presidential';
          price_per_night?: number;
          description?: string;
          amenities?: string[];
          max_occupancy?: number;
          images?: string[];
          status?: 'available' | 'booked' | 'maintenance' | 'cleaning';
          created_at?: string;
          updated_at?: string;
        };
      };
      reservations: {
        Row: {
          id: string;
          guest_id: string;
          room_id: string;
          check_in_date: string;
          check_out_date: string;
          total_amount: number;
          special_requests: string | null;
          status: 'pending' | 'confirmed' | 'checked_in' | 'checked_out' | 'cancelled';
          payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
          payment_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          guest_id: string;
          room_id: string;
          check_in_date: string;
          check_out_date: string;
          total_amount: number;
          special_requests?: string | null;
          status?: 'pending' | 'confirmed' | 'checked_in' | 'checked_out' | 'cancelled';
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded';
          payment_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          guest_id?: string;
          room_id?: string;
          check_in_date?: string;
          check_out_date?: string;
          total_amount?: number;
          special_requests?: string | null;
          status?: 'pending' | 'confirmed' | 'checked_in' | 'checked_out' | 'cancelled';
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded';
          payment_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      payments: {
        Row: {
          id: string;
          reservation_id: string;
          amount: number;
          currency: string;
          payment_method: 'card' | 'bank_transfer' | 'cash' | 'pos';
          paystack_reference: string | null;
          paystack_transaction_id: string | null;
          status: 'pending' | 'paid' | 'failed' | 'refunded';
          paid_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          reservation_id: string;
          amount: number;
          currency?: string;
          payment_method: 'card' | 'bank_transfer' | 'cash' | 'pos';
          paystack_reference?: string | null;
          paystack_transaction_id?: string | null;
          status?: 'pending' | 'paid' | 'failed' | 'refunded';
          paid_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          reservation_id?: string;
          amount?: number;
          currency?: string;
          payment_method?: 'card' | 'bank_transfer' | 'cash' | 'pos';
          paystack_reference?: string | null;
          paystack_transaction_id?: string | null;
          status?: 'pending' | 'paid' | 'failed' | 'refunded';
          paid_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      user_role: 'guest' | 'receptionist' | 'admin';
      room_type: 'standard' | 'deluxe' | 'suite' | 'presidential';
      room_status: 'available' | 'booked' | 'maintenance' | 'cleaning';
      reservation_status: 'pending' | 'confirmed' | 'checked_in' | 'checked_out' | 'cancelled';
      payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
      payment_method: 'card' | 'bank_transfer' | 'cash' | 'pos';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}

// Type helpers
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type InsertTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
export type UpdateTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];

// Entity types
export type User = Tables<'users'>;
export type Room = Tables<'rooms'>;
export type Reservation = Tables<'reservations'>;
export type Payment = Tables<'payments'>;

// Insert types
export type InsertUser = InsertTables<'users'>;
export type InsertRoom = InsertTables<'rooms'>;
export type InsertReservation = InsertTables<'reservations'>;
export type InsertPayment = InsertTables<'payments'>;

// Update types
export type UpdateUser = UpdateTables<'users'>;
export type UpdateRoom = UpdateTables<'rooms'>;
export type UpdateReservation = UpdateTables<'reservations'>;
export type UpdatePayment = UpdateTables<'payments'>;

// Enums
export type UserRole = Database['public']['Enums']['user_role'];
export type RoomType = Database['public']['Enums']['room_type'];
export type RoomStatus = Database['public']['Enums']['room_status'];
export type ReservationStatus = Database['public']['Enums']['reservation_status'];
export type PaymentStatus = Database['public']['Enums']['payment_status'];
export type PaymentMethod = Database['public']['Enums']['payment_method'];

// Filter types
export interface RoomFilter {
  room_type?: RoomType;
  min_price?: number;
  max_price?: number;
  max_occupancy?: number;
  status?: RoomStatus;
  check_in?: string;
  check_out?: string;
  amenities?: string[];
}

export interface ReservationFilter {
  status?: ReservationStatus;
  user_id?: string;
  room_id?: string;
  check_in_start?: string;
  check_in_end?: string;
  check_out_start?: string;
  check_out_end?: string;
}

// Extended types with relations
export interface ReservationWithDetails extends Reservation {
  room?: Room;
  user?: User;
  payment?: Payment;
}

export interface RoomWithReservations extends Room {
  reservations?: Reservation[];
}

// Notification types
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'booking' | 'payment' | 'reminder' | 'maintenance' | 'promotional' | 'general';
  read: boolean;
  createdAt: string;
  data?: Record<string, any>;
}
